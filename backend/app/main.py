import os
from pathlib import Path
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

from app.models.project_models import (
    IntelligentProjectRequest,
    IntelligentProjectResponse,
    ModificationRequest,
    ApplyModificationRequest,
)
from app.services.langgraph_project_generator import LangGraphProjectGenerator

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(
    title="AI Project Generator",
    description="Generate project skeletons using AI",
    version="1.0.0",
)

# Configure CORS
allowed_origins = os.getenv(
    "ALLOWED_ORIGINS", "http://localhost:3000,http://localhost:5173"
).split(",")

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize services
project_generator = LangGraphProjectGenerator()

# Create generated_projects directory
generated_projects_dir = Path("generated_projects")
generated_projects_dir.mkdir(exist_ok=True)


@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "AI Project Generator API", "version": "1.0.0", "docs": "/docs"}


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "AI Project Generator"}


@app.post("/api/generate/intelligent", response_model=IntelligentProjectResponse)
async def generate_intelligent_project(request: IntelligentProjectRequest):
    """Generate a new project using intelligent AI analysis"""
    try:
        # Validate request
        if not request.prompt.strip():
            raise HTTPException(status_code=400, detail="Project prompt is required")

        # Start intelligent project generation
        response = await project_generator.generate_intelligent_project(request)

        return response

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to generate intelligent project: {str(e)}"
        )


@app.get("/api/projects")
async def list_projects():
    """List all generated intelligent projects"""
    projects = []

    # Add intelligent projects
    for project_id, status in project_generator.generation_status.items():
        project_info = {
            "project_id": project_id,
            "project_type": "intelligent",
            "status": status.status,
            "progress": status.progress,
            "current_step": status.current_step,
            "phase": getattr(status, "phase", "unknown"),
            "created_at": getattr(status, "created_at", None),
        }

        # Add analysis info if available
        if project_id in project_generator.project_analyses:
            analysis = project_generator.project_analyses[project_id]
            project_info.update(
                {
                    "project_name": analysis.suggested_name,
                    "suggested_name": analysis.suggested_name,
                    "project_description": f"{analysis.project_type.title()} application",
                    "analysis": {
                        "suggested_name": analysis.suggested_name,
                        "project_type": analysis.project_type,
                        "complexity": analysis.estimated_complexity,
                        "key_features": (
                            analysis.key_features[:3] if analysis.key_features else []
                        ),  # First 3 features
                        "technology_stack": {
                            "frontend": (
                                analysis.technology_stack.frontend
                                if analysis.technology_stack
                                else []
                            ),
                            "backend": (
                                analysis.technology_stack.backend
                                if analysis.technology_stack
                                else []
                            ),
                            "database": (
                                analysis.technology_stack.database
                                if analysis.technology_stack
                                else []
                            ),
                        },
                    },
                }
            )
        else:
            # Fallback info when no analysis available
            project_info.update(
                {
                    "project_name": f"Project {project_id[:8]}",
                    "suggested_name": f"ai-project-{project_id[:8]}",
                    "project_description": "AI-generated project",
                }
            )

        # Check download availability
        if status.status == "completed":
            # Check if ZIP file exists in output directory
            project_dir = (
                project_generator.output_dir
                / f"{analysis.suggested_name if analysis else 'project'}_{project_id[:8]}"
            )
            zip_path = project_dir.with_suffix(".zip")
            if zip_path.exists():
                project_info["download_available"] = True
                project_info["file_size"] = zip_path.stat().st_size

                # Add file count if available
                if hasattr(status, "files") and status.files:
                    project_info["file_count"] = len(status.files)
            else:
                project_info["download_available"] = False

        # Add error info if failed
        if status.status == "failed" and hasattr(status, "error_message"):
            project_info["error_message"] = status.error_message

        projects.append(project_info)

    # Sort by creation time (newest first) if available, otherwise by project_id
    projects.sort(key=lambda x: x.get("created_at") or x["project_id"], reverse=True)

    return {
        "projects": projects,
        "total_count": len(projects),
        "intelligent_count": len(projects),
    }


@app.delete("/api/projects/{project_id}")
async def delete_project(project_id: str):
    """Delete a generated project (both classic and intelligent)"""
    try:
        deleted_from = []

        # Remove from project generator
        if project_id in project_generator.generation_status:
            del project_generator.generation_status[project_id]
            deleted_from.append("intelligent")

        # Remove from project analyses
        if project_id in project_generator.project_analyses:
            del project_generator.project_analyses[project_id]

        # Remove from project files
        if (
            hasattr(project_generator, "project_files")
            and project_id in project_generator.project_files
        ):
            del project_generator.project_files[project_id]

        # Remove from modification suggestions
        if (
            hasattr(project_generator, "modification_suggestions")
            and project_id in project_generator.modification_suggestions
        ):
            del project_generator.modification_suggestions[project_id]

        # Delete project files from both possible locations
        classic_project_dir = generated_projects_dir / project_id
        classic_zip_path = generated_projects_dir / f"{project_id}.zip"

        intelligent_project_dir = project_generator.output_dir / project_id
        intelligent_zip_path = project_generator.output_dir / f"{project_id}.zip"

        # Delete classic project files
        if classic_project_dir.exists():
            import shutil

            shutil.rmtree(classic_project_dir)

        if classic_zip_path.exists():
            classic_zip_path.unlink()

        # Delete intelligent project files
        if intelligent_project_dir.exists():
            import shutil

            shutil.rmtree(intelligent_project_dir)

        if intelligent_zip_path.exists():
            intelligent_zip_path.unlink()

        if not deleted_from:
            raise HTTPException(status_code=404, detail="Project not found")

        return {
            "message": "Project deleted successfully",
            "deleted_from": deleted_from,
            "project_id": project_id,
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to delete project: {str(e)}"
        )


@app.get("/api/status/enhanced/{project_id}")
async def get_enhanced_generation_status(project_id: str):
    """Get the enhanced generation status for an intelligent project"""
    try:
        status = project_generator.get_enhanced_generation_status(project_id)

        if not status:
            raise HTTPException(status_code=404, detail="Project not found")

        # Create a safe serializable version of the status
        status_dict = {
            "project_id": status.project_id,
            "status": status.status,
            "progress": status.progress,
            "current_step": status.current_step,
            "phase": status.phase,
            "estimated_time_remaining": status.estimated_time_remaining,
            "error_message": status.error_message,
            "can_modify": status.can_modify,
        }

        # Add analysis if available (safely)
        if status.analysis:
            try:
                if hasattr(status.analysis, "model_dump"):
                    status_dict["analysis"] = status.analysis.model_dump()
                else:
                    status_dict["analysis"] = {
                        "suggested_name": status.analysis.suggested_name,
                        "project_type": status.analysis.project_type,
                        "estimated_complexity": status.analysis.estimated_complexity,
                        "key_features": (
                            status.analysis.key_features[:3]
                            if status.analysis.key_features
                            else []
                        ),
                    }
            except Exception as analysis_error:
                print(f"DEBUG: Analysis serialization error: {analysis_error}")
                status_dict["analysis"] = None

        # Add pending modifications count (safely)
        if status.pending_modifications:
            try:
                status_dict["pending_modifications_count"] = len(
                    status.pending_modifications
                )
            except Exception:
                status_dict["pending_modifications_count"] = 0

        # Add applied modifications count (safely)
        if status.applied_modifications:
            try:
                status_dict["applied_modifications_count"] = len(
                    status.applied_modifications
                )
            except Exception:
                status_dict["applied_modifications_count"] = 0

        return status_dict

    except HTTPException:
        raise
    except Exception as e:
        print(f"DEBUG: Enhanced status error: {e}")
        import traceback

        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail=f"Error retrieving status: {str(e)}"
        )


@app.get("/api/analysis/{project_id}")
async def get_project_analysis(project_id: str):
    """Get the AI analysis for a project"""
    try:
        print(f"DEBUG: Requesting analysis for project {project_id}")

        # Check if project exists in project generator
        if not hasattr(project_generator, "project_analyses"):
            print("DEBUG: No project_analyses attribute found")
            raise HTTPException(status_code=404, detail="No analyses available")

        print(
            f"DEBUG: Available analyses: {list(project_generator.project_analyses.keys())}"
        )

        analysis = project_generator.get_project_analysis(project_id)
        print(f"DEBUG: Retrieved analysis: {analysis}")

        if not analysis:
            # Check if project exists in generation status but analysis is missing
            if project_id in project_generator.generation_status:
                status = project_generator.generation_status[project_id]
                print(
                    f"DEBUG: Project exists with status: {status.status}, phase: {status.phase}"
                )

                if status.status == "failed":
                    raise HTTPException(
                        status_code=500,
                        detail=f"Project generation failed: {status.error_message}",
                    )
                elif status.status in ["generating", "pending"]:
                    raise HTTPException(
                        status_code=202,
                        detail="Project is still being generated. Analysis not yet available.",
                    )
                else:
                    # Project completed but analysis is missing - this shouldn't happen
                    raise HTTPException(
                        status_code=500,
                        detail="Project completed but analysis is missing. This may indicate a generation error.",
                    )
            else:
                raise HTTPException(status_code=404, detail="Project not found")

        # Ensure we return a proper dict that FastAPI can serialize
        if hasattr(analysis, "model_dump"):
            return analysis.model_dump()
        else:
            return analysis
    except HTTPException:
        raise
    except Exception as e:
        print(f"DEBUG: Exception in get_project_analysis: {e}")
        import traceback

        traceback.print_exc()
        raise HTTPException(
            status_code=500, detail=f"Error retrieving analysis: {str(e)}"
        )


@app.post("/api/modify/{project_id}")
async def request_project_modifications(project_id: str, request: ModificationRequest):
    """Request modifications to an existing project"""
    try:
        request.project_id = project_id
        # TODO: Implement modifications for LangGraph generator
        raise HTTPException(
            status_code=501,
            detail="Modifications not yet implemented for LangGraph generator",
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to request modifications: {str(e)}"
        )


@app.post("/api/apply-modifications/{project_id}")
async def apply_project_modifications(
    project_id: str, request: ApplyModificationRequest
):
    """Apply specific modifications to a project"""
    try:
        request.project_id = project_id
        # TODO: Implement modifications for LangGraph generator
        raise HTTPException(
            status_code=501,
            detail="Modifications not yet implemented for LangGraph generator",
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to apply modifications: {str(e)}"
        )


@app.get("/api/suggestions/{project_id}")
async def get_modification_suggestions(project_id: str):
    """Get modification suggestions for a project"""
    try:
        # TODO: Implement suggestions for LangGraph generator
        raise HTTPException(
            status_code=501,
            detail="Suggestions not yet implemented for LangGraph generator",
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to get suggestions: {str(e)}"
        )


@app.get("/api/download/{project_id}")
async def download_project(project_id: str):
    """Download the generated project as a ZIP file"""
    try:
        # Check if project exists
        if project_id not in project_generator.generation_status:
            raise HTTPException(status_code=404, detail="Project not found")

        status = project_generator.generation_status[project_id]
        if status.status != "completed":
            raise HTTPException(
                status_code=400, detail="Project generation not completed"
            )

        # Get analysis for project name
        analysis = project_generator.get_project_analysis(project_id)
        project_name = analysis.suggested_name if analysis else "project"

        # Find the ZIP file
        project_dir = project_generator.output_dir / f"{project_name}_{project_id[:8]}"
        zip_path = project_dir.with_suffix(".zip")

        if not zip_path.exists():
            raise HTTPException(status_code=404, detail="Project ZIP file not found")

        from fastapi.responses import FileResponse

        return FileResponse(
            path=str(zip_path),
            filename=f"{project_name}.zip",
            media_type="application/zip",
        )

    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Failed to download project: {str(e)}"
        )


# Error handlers
@app.exception_handler(404)
async def not_found_handler(request, exc):
    from fastapi.responses import JSONResponse

    return JSONResponse(
        status_code=404, content={"error": "Not found", "detail": str(exc)}
    )


@app.exception_handler(500)
async def internal_error_handler(request, exc):
    from fastapi.responses import JSONResponse

    return JSONResponse(
        status_code=500, content={"error": "Internal server error", "detail": str(exc)}
    )


if __name__ == "__main__":
    import uvicorn

    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    debug = os.getenv("DEBUG", "True").lower() == "true"

    # Disable reload to prevent any file changes from restarting the server
    uvicorn.run(
        "app.main:app",
        host=host,
        port=port,
        reload=False,  # Completely disable reload
        log_level="info",
    )
